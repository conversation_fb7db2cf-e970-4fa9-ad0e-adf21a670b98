// 模拟数据生成器
export class MockDataGenerator {
  constructor() {
    this.idcRooms = [
      { id: 'idc-bj-01', name: '北京机房1', location: '北京', coordinates: [116.4074, 39.9042] },
      { id: 'idc-sh-01', name: '上海机房1', location: '上海', coordinates: [121.4737, 31.2304] },
      { id: 'idc-gz-01', name: '广州机房1', location: '广州', coordinates: [113.2644, 23.1291] },
      { id: 'idc-sz-01', name: '深圳机房1', location: '深圳', coordinates: [114.0579, 22.5431] },
      { id: 'idc-hz-01', name: '杭州机房1', location: '杭州', coordinates: [120.1551, 30.2741] },
    ];

    this.serverTypes = ['Web服务器', 'GPU计算节点', '数据库服务器', '存储服务器', '负载均衡器'];
    this.alertLevels = ['critical', 'warning', 'info'];
    this.alertTypes = ['CPU过载', 'GPU温度过高', '内存不足', '磁盘空间不足', '网络异常', '服务异常'];
  }

  // 生成随机数
  random(min, max) {
    return Math.random() * (max - min) + min;
  }

  // 生成随机整数
  randomInt(min, max) {
    return Math.floor(this.random(min, max));
  }

  // 生成总览统计数据
  generateOverviewStats() {
    const totalServers = this.randomInt(800, 1200);
    const onlineServers = this.randomInt(Math.floor(totalServers * 0.85), totalServers);
    
    return {
      totalServers,
      onlineServers,
      onlineRate: ((onlineServers / totalServers) * 100).toFixed(1),
      avgCpuUsage: this.random(45, 75).toFixed(1),
      avgGpuUsage: this.random(30, 85).toFixed(1),
      avgMemoryUsage: this.random(55, 80).toFixed(1),
      networkTraffic: this.random(500, 2000).toFixed(0) + ' Mbps',
      activeAlerts: this.randomInt(5, 25)
    };
  }

  // 生成IDC机房分布数据
  generateIdcDistribution() {
    return this.idcRooms.map(room => ({
      ...room,
      serverCount: this.randomInt(80, 250),
      onlineCount: this.randomInt(70, 240),
      cpuUsage: this.random(30, 80).toFixed(1),
      gpuUsage: this.random(20, 90).toFixed(1),
      status: this.random(0, 1) > 0.1 ? 'normal' : 'warning'
    }));
  }

  // 生成CPU使用率历史数据
  generateCpuUsageHistory(hours = 24) {
    const data = [];
    const now = new Date();
    
    for (let i = hours; i >= 0; i--) {
      const time = new Date(now.getTime() - i * 60 * 60 * 1000);
      const timeStr = time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
      
      data.push({
        time: timeStr,
        server1: this.random(20, 80).toFixed(1),
        server2: this.random(25, 75).toFixed(1),
        server3: this.random(30, 85).toFixed(1),
        average: this.random(35, 70).toFixed(1)
      });
    }
    
    return data;
  }

  // 生成GPU使用率历史数据
  generateGpuUsageHistory(hours = 24) {
    const data = [];
    const now = new Date();
    
    for (let i = hours; i >= 0; i--) {
      const time = new Date(now.getTime() - i * 60 * 60 * 1000);
      const timeStr = time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
      
      data.push({
        time: timeStr,
        gpu1: this.random(10, 95).toFixed(1),
        gpu2: this.random(15, 90).toFixed(1),
        gpu3: this.random(20, 85).toFixed(1),
        gpu4: this.random(5, 80).toFixed(1),
        average: this.random(25, 75).toFixed(1)
      });
    }
    
    return data;
  }

  // 生成内存使用分布数据
  generateMemoryUsage() {
    const total = 1024; // GB
    const used = this.random(400, 800);
    const cached = this.random(50, 150);
    const free = total - used - cached;
    
    return [
      { name: '已使用', value: used.toFixed(0), percentage: (used / total * 100).toFixed(1) },
      { name: '缓存', value: cached.toFixed(0), percentage: (cached / total * 100).toFixed(1) },
      { name: '空闲', value: free.toFixed(0), percentage: (free / total * 100).toFixed(1) }
    ];
  }

  // 生成网络流量数据
  generateNetworkTraffic(hours = 24) {
    const data = [];
    const now = new Date();
    
    for (let i = hours; i >= 0; i--) {
      const time = new Date(now.getTime() - i * 60 * 60 * 1000);
      const timeStr = time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
      
      data.push({
        time: timeStr,
        inbound: this.random(100, 800).toFixed(0),
        outbound: this.random(80, 600).toFixed(0)
      });
    }
    
    return data;
  }

  // 生成告警信息
  generateAlerts(count = 10) {
    const alerts = [];
    const now = new Date();
    
    for (let i = 0; i < count; i++) {
      const alertTime = new Date(now.getTime() - this.randomInt(0, 24 * 60 * 60 * 1000));
      const level = this.alertLevels[this.randomInt(0, this.alertLevels.length)];
      const type = this.alertTypes[this.randomInt(0, this.alertTypes.length)];
      const room = this.idcRooms[this.randomInt(0, this.idcRooms.length)];
      
      alerts.push({
        id: `alert-${i + 1}`,
        level,
        type,
        message: `${room.name} - ${type}`,
        server: `${room.id}-server-${this.randomInt(1, 100).toString().padStart(3, '0')}`,
        time: alertTime.toLocaleString('zh-CN'),
        status: this.random(0, 1) > 0.3 ? 'active' : 'resolved'
      });
    }
    
    return alerts.sort((a, b) => new Date(b.time) - new Date(a.time));
  }

  // 生成服务器状态列表
  generateServerStatus(count = 20) {
    const servers = [];
    
    for (let i = 0; i < count; i++) {
      const room = this.idcRooms[this.randomInt(0, this.idcRooms.length)];
      const serverType = this.serverTypes[this.randomInt(0, this.serverTypes.length)];
      const status = this.random(0, 1) > 0.1 ? 'online' : (this.random(0, 1) > 0.5 ? 'offline' : 'maintenance');
      
      servers.push({
        id: `${room.id}-server-${(i + 1).toString().padStart(3, '0')}`,
        name: `${room.location}服务器${i + 1}`,
        type: serverType,
        room: room.name,
        status,
        cpuUsage: status === 'online' ? this.random(10, 90).toFixed(1) : '0',
        gpuUsage: status === 'online' ? this.random(0, 95).toFixed(1) : '0',
        memoryUsage: status === 'online' ? this.random(30, 85).toFixed(1) : '0',
        uptime: status === 'online' ? `${this.randomInt(1, 365)}天` : '0天',
        lastUpdate: new Date(Date.now() - this.randomInt(0, 300000)).toLocaleTimeString('zh-CN')
      });
    }
    
    return servers;
  }

  // 获取所有模拟数据
  getAllData() {
    return {
      overview: this.generateOverviewStats(),
      idcDistribution: this.generateIdcDistribution(),
      cpuHistory: this.generateCpuUsageHistory(),
      gpuHistory: this.generateGpuUsageHistory(),
      memoryUsage: this.generateMemoryUsage(),
      networkTraffic: this.generateNetworkTraffic(),
      alerts: this.generateAlerts(),
      servers: this.generateServerStatus()
    };
  }
}

// 创建全局实例
export const mockDataGenerator = new MockDataGenerator();

// 导出便捷方法
export const getMockData = () => mockDataGenerator.getAllData();
