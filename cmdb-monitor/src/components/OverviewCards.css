.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
  padding: 0 20px;
}

.overview-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.card-icon {
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.8);
}

.card-title {
  font-size: 14px;
  color: #666;
  font-weight: 500;
  line-height: 1.4;
}

.card-content {
  display: flex;
  flex-direction: column;
}

.card-value {
  font-size: 32px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 4px;
  display: flex;
  align-items: baseline;
}

.card-unit {
  font-size: 16px;
  font-weight: 400;
  margin-left: 4px;
  opacity: 0.8;
}

.card-subtitle {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .overview-cards {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 16px;
  }
  
  .overview-card {
    padding: 20px;
  }
  
  .card-value {
    font-size: 28px;
  }
}

@media (max-width: 768px) {
  .overview-cards {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    padding: 0 16px;
  }
  
  .overview-card {
    padding: 16px;
  }
  
  .card-value {
    font-size: 24px;
  }
  
  .card-icon {
    width: 32px;
    height: 32px;
  }
}

/* 动画效果 */
.overview-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 为不同的卡片添加延迟动画 */
.overview-card:nth-child(1) { animation-delay: 0.1s; }
.overview-card:nth-child(2) { animation-delay: 0.2s; }
.overview-card:nth-child(3) { animation-delay: 0.3s; }
.overview-card:nth-child(4) { animation-delay: 0.4s; }
.overview-card:nth-child(5) { animation-delay: 0.5s; }
.overview-card:nth-child(6) { animation-delay: 0.6s; }
.overview-card:nth-child(7) { animation-delay: 0.7s; }
