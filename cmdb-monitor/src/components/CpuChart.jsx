import React, { useMemo, useState } from 'react';
import ReactECharts from 'echarts-for-react';
import ChartControls, { TimeRangeSelector, DataFilter } from './ChartControls';
import { notificationManager } from '../utils/notification';
import './CpuChart.css';

const CpuChart = ({ data, title = 'CPU使用率趋势', onRefresh }) => {
  const [timeRange, setTimeRange] = useState('24h');
  const [activeFilters, setActiveFilters] = useState(['server1', 'server2', 'server3', 'average']);
  const [isAutoRefresh, setIsAutoRefresh] = useState(true);

  const dataFilters = [
    { id: 'server1', label: '服务器1', color: '#1890ff' },
    { id: 'server2', label: '服务器2', color: '#52c41a' },
    { id: 'server3', label: '服务器3', color: '#fa8c16' },
    { id: 'average', label: '平均值', color: '#722ed1' }
  ];
  const chartOption = useMemo(() => {
    if (!data || !Array.isArray(data)) return {};

    const times = data.map(item => item.time);
    const server1Data = data.map(item => parseFloat(item.server1));
    const server2Data = data.map(item => parseFloat(item.server2));
    const server3Data = data.map(item => parseFloat(item.server3));
    const averageData = data.map(item => parseFloat(item.average));

    return {
      title: {
        text: title,
        left: 'center',
        top: 20,
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold',
          color: '#333'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        },
        formatter: function(params) {
          let result = `<div style="padding: 8px;"><strong>${params[0].axisValue}</strong><br/>`;
          params.forEach(param => {
            result += `<div style="margin: 4px 0;">
              <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${param.color};"></span>
              ${param.seriesName}: ${param.value}%
            </div>`;
          });
          result += '</div>';
          return result;
        },
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#ccc',
        borderWidth: 1,
        textStyle: {
          color: '#333'
        }
      },
      legend: {
        data: ['服务器1', '服务器2', '服务器3', '平均值'],
        top: 50,
        textStyle: {
          color: '#666'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '25%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: times,
        axisLine: {
          lineStyle: {
            color: '#ddd'
          }
        },
        axisLabel: {
          color: '#666',
          fontSize: 12
        }
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 100,
        axisLine: {
          lineStyle: {
            color: '#ddd'
          }
        },
        axisLabel: {
          color: '#666',
          fontSize: 12,
          formatter: '{value}%'
        },
        splitLine: {
          lineStyle: {
            color: '#f0f0f0',
            type: 'dashed'
          }
        }
      },
      series: [
        {
          name: '服务器1',
          type: 'line',
          data: server1Data,
          smooth: true,
          lineStyle: {
            color: '#1890ff',
            width: 2
          },
          itemStyle: {
            color: '#1890ff'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
                { offset: 1, color: 'rgba(24, 144, 255, 0.05)' }
              ]
            }
          }
        },
        {
          name: '服务器2',
          type: 'line',
          data: server2Data,
          smooth: true,
          lineStyle: {
            color: '#52c41a',
            width: 2
          },
          itemStyle: {
            color: '#52c41a'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(82, 196, 26, 0.3)' },
                { offset: 1, color: 'rgba(82, 196, 26, 0.05)' }
              ]
            }
          }
        },
        {
          name: '服务器3',
          type: 'line',
          data: server3Data,
          smooth: true,
          lineStyle: {
            color: '#fa8c16',
            width: 2
          },
          itemStyle: {
            color: '#fa8c16'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(250, 140, 22, 0.3)' },
                { offset: 1, color: 'rgba(250, 140, 22, 0.05)' }
              ]
            }
          }
        },
        {
          name: '平均值',
          type: 'line',
          data: averageData,
          smooth: true,
          lineStyle: {
            color: '#722ed1',
            width: 3,
            type: 'dashed'
          },
          itemStyle: {
            color: '#722ed1',
            borderWidth: 2
          },
          symbol: 'circle',
          symbolSize: 6
        }
      ]
    };
  }, [data, title]);

  // 计算统计信息
  const stats = useMemo(() => {
    if (!data || !Array.isArray(data) || data.length === 0) return null;
    
    const latest = data[data.length - 1];
    const avgCpu = parseFloat(latest.average);
    const maxCpu = Math.max(
      parseFloat(latest.server1),
      parseFloat(latest.server2),
      parseFloat(latest.server3)
    );
    const minCpu = Math.min(
      parseFloat(latest.server1),
      parseFloat(latest.server2),
      parseFloat(latest.server3)
    );
    
    return {
      current: avgCpu.toFixed(1),
      max: maxCpu.toFixed(1),
      min: minCpu.toFixed(1),
      status: avgCpu > 80 ? 'danger' : avgCpu > 60 ? 'warning' : 'normal'
    };
  }, [data]);

  if (!data || !Array.isArray(data)) {
    return (
      <div className="cpu-chart">
        <div className="loading">加载中...</div>
      </div>
    );
  }

  // 处理图表控制事件
  const handleRefresh = () => {
    onRefresh && onRefresh();
    notificationManager.info('CPU数据已刷新');
  };

  const handleToggleAutoRefresh = () => {
    setIsAutoRefresh(!isAutoRefresh);
    notificationManager.info(isAutoRefresh ? '已暂停自动刷新' : '已开启自动刷新');
  };

  const handleExport = (format) => {
    notificationManager.info(`正在导出${format.toUpperCase()}格式...`);
    // 这里可以实现实际的导出逻辑
  };

  const handleFullscreen = () => {
    notificationManager.info('全屏功能开发中...');
  };

  return (
    <div className="cpu-chart">
      <div className="chart-header">
        <div className="chart-title-section">
          <h3>{title}</h3>
          <TimeRangeSelector
            value={timeRange}
            onChange={setTimeRange}
          />
        </div>
        <div className="chart-controls-section">
          <DataFilter
            filters={dataFilters}
            activeFilters={activeFilters}
            onChange={setActiveFilters}
          />
          <ChartControls
            onRefresh={handleRefresh}
            onToggleAutoRefresh={handleToggleAutoRefresh}
            onExport={handleExport}
            onFullscreen={handleFullscreen}
            isAutoRefresh={isAutoRefresh}
          />
        </div>
      </div>

      <div className="chart-container">
        <ReactECharts
          option={chartOption}
          style={{ height: '300px', width: '100%' }}
          opts={{ renderer: 'canvas' }}
        />
      </div>

      {stats && (
        <div className="chart-stats">
          <div className="stat-item">
            <span className="stat-label">当前平均</span>
            <span className={`stat-value ${stats.status}`}>{stats.current}%</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">最高</span>
            <span className="stat-value">{stats.max}%</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">最低</span>
            <span className="stat-value">{stats.min}%</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default CpuChart;
