import React from 'react';
import { RefreshCw, Clock } from 'lucide-react';
import { useRealTimeData } from '../hooks/useRealTimeData';
import OverviewCards from './OverviewCards';
import IdcDistribution from './IdcDistribution';
import C<PERSON><PERSON><PERSON> from './CpuChart';
import Gpu<PERSON><PERSON> from './GpuChart';
import MemoryChart from './MemoryChart';
import NetworkChart from './NetworkChart';
import AlertList from './AlertList';
import ServerStatus from './ServerStatus';
import './Dashboard.css';

const Dashboard = () => {
  const { data, loading, lastUpdate, refreshData } = useRealTimeData(30000); // 30秒更新一次

  const handleRefresh = () => {
    refreshData();
  };

  if (loading && !data) {
    return (
      <div className="dashboard-loading">
        <div className="loading-spinner">
          <RefreshCw size={48} className="spinning" />
          <p>正在加载监控数据...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="dashboard">
      {/* 头部 */}
      <header className="dashboard-header">
        <div className="header-left">
          <h1 className="dashboard-title">CMDB资产管理监控大屏</h1>
          <div className="header-subtitle">
            实时监控IDC机房资源状态和性能指标
          </div>
        </div>
        
        <div className="header-right">
          {lastUpdate && (
            <div className="last-update">
              <Clock size={16} />
              <span>最后更新: {lastUpdate.toLocaleTimeString('zh-CN')}</span>
            </div>
          )}
          
          <button 
            className="refresh-button" 
            onClick={handleRefresh}
            disabled={loading}
          >
            <RefreshCw size={16} className={loading ? 'spinning' : ''} />
            刷新数据
          </button>
        </div>
      </header>

      {/* 主要内容区域 */}
      <main className="dashboard-main">
        {/* 总览统计卡片 */}
        <section className="overview-section">
          <OverviewCards data={data?.overview} />
        </section>

        {/* 第一行：IDC分布、CPU、GPU */}
        <section className="charts-row-1">
          <div className="chart-item idc-chart">
            <IdcDistribution data={data?.idcDistribution} />
          </div>
          
          <div className="chart-item cpu-chart">
            <CpuChart data={data?.cpuHistory} />
          </div>
          
          <div className="chart-item gpu-chart">
            <GpuChart data={data?.gpuHistory} />
          </div>
        </section>

        {/* 第二行：内存、网络、告警 */}
        <section className="charts-row-2">
          <div className="chart-item memory-chart">
            <MemoryChart data={data?.memoryUsage} />
          </div>
          
          <div className="chart-item network-chart">
            <NetworkChart data={data?.networkTraffic} />
          </div>
          
          <div className="chart-item alert-chart">
            <AlertList data={data?.alerts} />
          </div>
        </section>

        {/* 第三行：服务器状态表格 */}
        <section className="server-section">
          <ServerStatus 
            data={data?.servers} 
            onRefresh={handleRefresh}
          />
        </section>
      </main>

      {/* 页脚 */}
      <footer className="dashboard-footer">
        <div className="footer-content">
          <span>© 2024 CMDB资产管理系统</span>
          <span>数据更新频率: 30秒</span>
          <span>在线监控服务器: {data?.overview?.onlineServers || 0} 台</span>
        </div>
      </footer>
    </div>
  );
};

export default Dashboard;
