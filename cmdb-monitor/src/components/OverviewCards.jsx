import React from 'react';
import { Server, Activity, Cpu, HardDrive, Wifi, AlertTriangle } from 'lucide-react';
import './OverviewCards.css';

const OverviewCards = ({ data }) => {
  if (!data) return null;

  const cards = [
    {
      title: '总服务器数',
      value: data.totalServers,
      unit: '台',
      icon: Server,
      color: '#1890ff',
      bgColor: 'rgba(24, 144, 255, 0.1)'
    },
    {
      title: '在线率',
      value: data.onlineRate,
      unit: '%',
      icon: Activity,
      color: '#52c41a',
      bgColor: 'rgba(82, 196, 26, 0.1)',
      subtitle: `${data.onlineServers}/${data.totalServers} 在线`
    },
    {
      title: 'CPU平均使用率',
      value: data.avgCpuUsage,
      unit: '%',
      icon: Cpu,
      color: '#fa8c16',
      bgColor: 'rgba(250, 140, 22, 0.1)'
    },
    {
      title: 'GPU平均使用率',
      value: data.avgGpuUsage,
      unit: '%',
      icon: HardDrive,
      color: '#722ed1',
      bgColor: 'rgba(114, 46, 209, 0.1)'
    },
    {
      title: '内存平均使用率',
      value: data.avgMemoryUsage,
      unit: '%',
      icon: Activity,
      color: '#13c2c2',
      bgColor: 'rgba(19, 194, 194, 0.1)'
    },
    {
      title: '网络流量',
      value: data.networkTraffic.split(' ')[0],
      unit: 'Mbps',
      icon: Wifi,
      color: '#eb2f96',
      bgColor: 'rgba(235, 47, 150, 0.1)'
    },
    {
      title: '活跃告警',
      value: data.activeAlerts,
      unit: '条',
      icon: AlertTriangle,
      color: '#f5222d',
      bgColor: 'rgba(245, 34, 45, 0.1)'
    }
  ];

  return (
    <div className="overview-cards">
      {cards.map((card, index) => {
        const IconComponent = card.icon;
        return (
          <div key={index} className="overview-card" style={{ backgroundColor: card.bgColor }}>
            <div className="card-header">
              <div className="card-icon" style={{ color: card.color }}>
                <IconComponent size={24} />
              </div>
              <div className="card-title">{card.title}</div>
            </div>
            <div className="card-content">
              <div className="card-value" style={{ color: card.color }}>
                {card.value}
                <span className="card-unit">{card.unit}</span>
              </div>
              {card.subtitle && (
                <div className="card-subtitle">{card.subtitle}</div>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default OverviewCards;
