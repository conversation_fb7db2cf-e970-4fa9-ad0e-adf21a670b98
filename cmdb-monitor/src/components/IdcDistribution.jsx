import React, { useMemo } from 'react';
import ReactECharts from 'echarts-for-react';
import './IdcDistribution.css';

const IdcDistribution = ({ data }) => {
  const chartOption = useMemo(() => {
    if (!data || !Array.isArray(data)) return {};

    // 准备地图数据
    const mapData = data.map(item => ({
      name: item.location,
      value: [item.coordinates[0], item.coordinates[1], item.serverCount],
      itemStyle: {
        color: item.status === 'normal' ? '#52c41a' : '#faad14'
      },
      label: {
        show: true,
        formatter: `{b}\n{c}台`,
        fontSize: 12,
        color: '#333'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 14,
          fontWeight: 'bold'
        }
      }
    }));

    return {
      title: {
        text: 'IDC机房分布',
        left: 'center',
        top: 20,
        textStyle: {
          fontSize: 18,
          fontWeight: 'bold',
          color: '#333'
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: function(params) {
          const item = data.find(d => d.location === params.name);
          if (!item) return '';
          
          return `
            <div style="padding: 8px;">
              <div style="font-weight: bold; margin-bottom: 8px;">${item.name}</div>
              <div>服务器总数: ${item.serverCount}台</div>
              <div>在线数量: ${item.onlineCount}台</div>
              <div>CPU使用率: ${item.cpuUsage}%</div>
              <div>GPU使用率: ${item.gpuUsage}%</div>
              <div>状态: ${item.status === 'normal' ? '正常' : '告警'}</div>
            </div>
          `;
        },
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#ccc',
        borderWidth: 1,
        textStyle: {
          color: '#333'
        }
      },
      grid: {
        left: '10%',
        right: '10%',
        top: '20%',
        bottom: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        min: 110,
        max: 125,
        show: false
      },
      yAxis: {
        type: 'value',
        min: 20,
        max: 45,
        show: false
      },
      series: [
        {
          type: 'scatter',
          data: mapData,
          symbolSize: function(val) {
            return Math.max(val[2] / 8, 20);
          },
          symbol: 'circle',
          itemStyle: {
            borderColor: '#fff',
            borderWidth: 3,
            shadowBlur: 15,
            shadowColor: 'rgba(0, 0, 0, 0.3)'
          },
          emphasis: {
            scale: 1.3,
            itemStyle: {
              shadowBlur: 25,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          label: {
            show: true,
            position: 'top',
            formatter: function(params) {
              return `${params.name}\n${params.value[2]}台`;
            },
            fontSize: 12,
            color: '#333',
            fontWeight: 'bold'
          }
        },
        {
          type: 'effectScatter',
          data: mapData.filter(item => {
            const originalItem = data.find(d => d.location === item.name);
            return originalItem && originalItem.status === 'warning';
          }),
          symbolSize: function(val) {
            return Math.max(val[2] / 6, 25);
          },
          showEffectOn: 'render',
          rippleEffect: {
            brushType: 'stroke',
            scale: 3,
            period: 2
          },
          itemStyle: {
            color: '#faad14',
            shadowBlur: 15,
            shadowColor: '#faad14'
          }
        }
      ]
    };
  }, [data]);

  // 统计信息
  const stats = useMemo(() => {
    if (!data || !Array.isArray(data)) return null;
    
    const totalServers = data.reduce((sum, item) => sum + item.serverCount, 0);
    const totalOnline = data.reduce((sum, item) => sum + item.onlineCount, 0);
    const avgCpuUsage = (data.reduce((sum, item) => sum + parseFloat(item.cpuUsage), 0) / data.length).toFixed(1);
    const avgGpuUsage = (data.reduce((sum, item) => sum + parseFloat(item.gpuUsage), 0) / data.length).toFixed(1);
    const normalRooms = data.filter(item => item.status === 'normal').length;
    
    return {
      totalServers,
      totalOnline,
      onlineRate: ((totalOnline / totalServers) * 100).toFixed(1),
      avgCpuUsage,
      avgGpuUsage,
      normalRooms,
      totalRooms: data.length
    };
  }, [data]);

  if (!data || !Array.isArray(data)) {
    return (
      <div className="idc-distribution">
        <div className="loading">加载中...</div>
      </div>
    );
  }

  return (
    <div className="idc-distribution">
      <div className="chart-container">
        <ReactECharts 
          option={chartOption} 
          style={{ height: '400px', width: '100%' }}
          opts={{ renderer: 'canvas' }}
        />
      </div>
      
      {stats && (
        <div className="idc-stats">
          <div className="stat-item">
            <span className="stat-label">机房总数</span>
            <span className="stat-value">{stats.totalRooms}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">正常机房</span>
            <span className="stat-value">{stats.normalRooms}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">服务器总数</span>
            <span className="stat-value">{stats.totalServers}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">在线率</span>
            <span className="stat-value">{stats.onlineRate}%</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">平均CPU</span>
            <span className="stat-value">{stats.avgCpuUsage}%</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">平均GPU</span>
            <span className="stat-value">{stats.avgGpuUsage}%</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default IdcDistribution;
