# 🚀 CMDB监控大屏一键部署指南

## 📋 部署信息
- **目标服务器**: ************
- **操作系统**: Ubuntu 20.04+ (推荐)
- **Web服务器**: Node.js + serve
- **进程管理**: PM2
- **访问端口**: 3000
- **部署方式**: 静态文件部署

## ⚡ 快速部署（推荐）

### 1. 准备工作
```bash
# 确保项目已构建
npm run build:optimize

# 检查构建文件
ls -la dist/
```

### 2. 配置部署参数
编辑 `deploy/deploy.sh` 文件，修改以下参数：
```bash
SERVER_IP="************"        # 服务器IP
SERVER_USER="root"               # SSH用户名
PORT="3000"                      # Web服务端口
DOMAIN="cmdb.yourdomain.com"     # 域名（可选）
```

### 3. 执行一键部署
```bash
# 执行部署脚本
./deploy/deploy.sh
```

## 🔧 手动部署步骤

### 步骤1: 上传文件
```bash
# 创建部署包
tar -czf cmdb-monitor.tar.gz -C dist .

# 上传文件到服务器
scp cmdb-monitor.tar.gz root@************:/tmp/
scp deploy/pm2.config.js root@************:/tmp/
scp deploy/install-server.sh root@************:/tmp/
```

### 步骤2: 服务器配置
```bash
# SSH连接到服务器
ssh root@************

# 执行安装脚本
chmod +x /tmp/install-server.sh
/tmp/install-server.sh
```

## 🔐 SSL证书配置（可选）

### 配置域名SSL证书
```bash
# 在服务器上执行
./deploy/setup-ssl.sh

# 或手动配置
certbot --nginx -d cmdb.yourdomain.com
```

## 📊 部署验证

### 1. 检查服务状态
```bash
# 在服务器上执行
systemctl status nginx
ufw status
```

### 2. 访问测试
- **HTTP**: http://************:3000
- **域名**: http://cmdb.yourdomain.com:3000 (如果配置了域名)

### 3. 运行健康检查
```bash
# 在服务器上执行
./deploy/monitor.sh
```

## 🛠️ 常用维护命令

### 查看日志
```bash
# Nginx访问日志
tail -f /var/log/nginx/cmdb-monitor/access.log

# Nginx错误日志
tail -f /var/log/nginx/cmdb-monitor/error.log
```

### 重启服务
```bash
# 重启Nginx
systemctl restart nginx

# 重新加载配置
systemctl reload nginx
```

### 更新应用
```bash
# 1. 本地构建新版本
npm run build:optimize

# 2. 上传新文件
scp -r dist/* root@************:/var/www/cmdb-monitor/

# 3. 清理缓存（可选）
ssh root@************ "systemctl reload nginx"
```

## 🔍 故障排除

### 常见问题及解决方案

#### 1. 无法访问网站
```bash
# 检查Nginx状态
systemctl status nginx

# 检查防火墙
ufw status

# 检查端口监听
netstat -tulpn | grep :80
```

#### 2. 静态资源404错误
```bash
# 检查文件权限
ls -la /var/www/cmdb-monitor/

# 检查Nginx配置
nginx -t
```

#### 3. SSL证书问题
```bash
# 检查证书状态
certbot certificates

# 续期证书
certbot renew
```

## 📈 性能优化建议

### 1. 启用CDN
- 使用阿里云CDN加速静态资源
- 配置合适的缓存策略

### 2. 数据库优化（如果有后端）
- 配置Redis缓存
- 优化数据库查询

### 3. 监控告警
- 配置服务器监控
- 设置告警通知

## 🔄 自动化部署

### 设置CI/CD（可选）
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '16'
      - name: Install dependencies
        run: npm install
      - name: Build
        run: npm run build:optimize
      - name: Deploy
        run: ./deploy/deploy.sh
```

## 📞 技术支持

### 联系方式
- 技术文档: `deploy/README.md`
- 监控脚本: `deploy/monitor.sh`
- 配置文件: `deploy/nginx.conf`

### 备份策略
```bash
# 创建备份
tar -czf backup-$(date +%Y%m%d).tar.gz /var/www/cmdb-monitor

# 定时备份（添加到crontab）
0 2 * * * tar -czf /root/backups/cmdb-$(date +\%Y\%m\%d).tar.gz /var/www/cmdb-monitor
```

---

## ✅ 部署检查清单

- [ ] 服务器环境准备完成
- [ ] 项目构建成功
- [ ] 文件上传完成
- [ ] Nginx配置正确
- [ ] 防火墙规则设置
- [ ] 网站可正常访问
- [ ] SSL证书配置（可选）
- [ ] 监控脚本部署
- [ ] 备份策略设置

**部署完成后，请访问 http://************ 验证部署结果！**
