# 🚀 CMDB监控大屏快速部署指南（无Nginx版本）

## 📋 部署概述
- **目标服务器**: ************
- **Web服务器**: Node.js + serve
- **进程管理**: PM2
- **访问端口**: 3000
- **部署时间**: 约5分钟

## ⚡ 一键部署

### 方法一：自动部署（推荐）
```bash
# 1. 确保项目已构建
npm run build:optimize

# 2. 执行一键部署
./deploy/deploy.sh
```

### 方法二：手动部署
```bash
# 1. 构建项目
npm run build:optimize

# 2. 上传文件
tar -czf cmdb-monitor.tar.gz -C dist .
scp cmdb-monitor.tar.gz root@************:/tmp/
scp deploy/pm2.config.js root@************:/tmp/
scp deploy/install-server.sh root@************:/tmp/

# 3. 在服务器上安装
ssh root@************
chmod +x /tmp/install-server.sh
/tmp/install-server.sh
```

## 🔧 服务器要求
- Ubuntu 20.04+ / CentOS 7+
- 内存: 最低1GB，推荐2GB+
- 存储: 最低10GB可用空间
- 网络: 开放3000端口

## 📋 安装内容
部署脚本将自动安装：
- ✅ Node.js 18.x
- ✅ npm包管理器
- ✅ serve静态文件服务器
- ✅ PM2进程管理器
- ✅ 防火墙配置
- ✅ 应用自启动

## 🌐 访问地址
部署完成后访问：
- **主要地址**: http://************:3000
- **域名访问**: http://your-domain.com:3000 (如果配置了域名)

## 🔍 验证部署
```bash
# 检查部署状态
./deploy/check-deployment.sh

# 在服务器上检查服务
ssh root@************ "pm2 status"
```

## 📊 服务管理

### PM2常用命令
```bash
# 查看应用状态
pm2 status

# 查看日志
pm2 logs cmdb-monitor

# 重启应用
pm2 restart cmdb-monitor

# 停止应用
pm2 stop cmdb-monitor

# 删除应用
pm2 delete cmdb-monitor
```

### 服务器管理
```bash
# 查看端口占用
netstat -tulpn | grep :3000

# 查看防火墙状态
ufw status

# 查看系统资源
htop
```

## 🔧 故障排除

### 常见问题

#### 1. 无法访问网站
```bash
# 检查应用状态
pm2 status

# 检查端口
netstat -tulpn | grep :3000

# 检查防火墙
ufw status
```

#### 2. 应用启动失败
```bash
# 查看错误日志
pm2 logs cmdb-monitor

# 手动启动测试
cd /opt/cmdb-monitor
serve -s . -l 3000
```

#### 3. 端口被占用
```bash
# 查看端口占用
lsof -i :3000

# 杀死占用进程
kill -9 <PID>
```

## 🔄 更新应用

### 快速更新
```bash
# 1. 本地构建新版本
npm run build:optimize

# 2. 上传新文件
scp -r dist/* root@************:/opt/cmdb-monitor/

# 3. 重启应用
ssh root@************ "pm2 restart cmdb-monitor"
```

## 🛡️ 安全配置

### 防火墙设置
```bash
# 只允许必要端口
ufw allow ssh
ufw allow 3000/tcp
ufw enable
```

### 反向代理（可选）
如果需要使用80端口或HTTPS，可以配置Nginx反向代理：
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 📈 性能优化

### 1. 启用集群模式
编辑 `/opt/cmdb-monitor/pm2.config.js`：
```javascript
instances: 'max',
exec_mode: 'cluster'
```

### 2. 配置缓存
serve已内置缓存优化，无需额外配置。

### 3. 监控设置
```bash
# 安装PM2监控
pm2 install pm2-server-monit

# 查看监控
pm2 monit
```

## 📞 技术支持

### 日志位置
- 应用日志: `/var/log/cmdb-monitor/`
- PM2日志: `~/.pm2/logs/`

### 配置文件
- PM2配置: `/opt/cmdb-monitor/pm2.config.js`
- 应用目录: `/opt/cmdb-monitor/`

### 监控脚本
```bash
# 运行健康检查
./deploy/monitor.sh

# 简单启动（测试用）
./deploy/start-simple.sh
```

---

## ✅ 部署检查清单

- [ ] 服务器环境准备
- [ ] 项目构建完成
- [ ] 文件上传成功
- [ ] Node.js安装完成
- [ ] PM2配置正确
- [ ] 应用启动成功
- [ ] 防火墙配置
- [ ] 网站可正常访问
- [ ] 监控脚本部署

**部署完成后访问: http://************:3000** 🎉
