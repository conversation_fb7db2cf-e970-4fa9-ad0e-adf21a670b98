#!/bin/bash

# 简单启动脚本 - 不使用PM2
# 适用于快速测试或简单部署

PORT=${1:-3000}
HOST=${2:-0.0.0.0}

echo "🚀 启动CMDB监控大屏..."
echo "端口: $PORT"
echo "主机: $HOST"
echo ""

# 检查是否安装了serve
if ! command -v serve &> /dev/null; then
    echo "❌ serve未安装，正在安装..."
    npm install -g serve
fi

# 检查构建文件
if [ ! -f "index.html" ]; then
    echo "❌ 未找到index.html文件"
    echo "请确保在正确的目录中运行此脚本"
    exit 1
fi

echo "✅ 启动Web服务器..."
echo "访问地址: http://$HOST:$PORT"
echo "按 Ctrl+C 停止服务"
echo ""

# 启动服务
serve -s . -l $PORT -H $HOST --cors --no-clipboard
