#!/bin/bash

# CMDB监控大屏部署脚本（无Nginx版本）
# 目标服务器: ************

set -e

# 配置变量
SERVER_IP="************"
SERVER_USER="root"  # 根据实际情况修改
DEPLOY_PATH="/opt/cmdb-monitor"
PORT="3000"  # Web服务端口
DOMAIN="cmdb.yourdomain.com"  # 根据实际域名修改（可选）

echo "🚀 开始部署CMDB监控大屏到阿里云服务器..."

# 检查本地构建文件
if [ ! -d "dist" ]; then
    echo "❌ 未找到构建文件，请先运行 npm run build:optimize"
    exit 1
fi

echo "📦 准备部署文件..."

# 创建部署包
tar -czf cmdb-monitor-deploy.tar.gz -C dist .

echo "📤 上传文件到服务器..."

# 上传文件到服务器
scp cmdb-monitor-deploy.tar.gz ${SERVER_USER}@${SERVER_IP}:/tmp/
scp deploy/pm2.config.js ${SERVER_USER}@${SERVER_IP}:/tmp/
scp deploy/install-server.sh ${SERVER_USER}@${SERVER_IP}:/tmp/

echo "🔧 在服务器上执行安装..."

# 在服务器上执行安装脚本
ssh ${SERVER_USER}@${SERVER_IP} "chmod +x /tmp/install-server.sh && /tmp/install-server.sh"

echo "🧹 清理临时文件..."
rm -f cmdb-monitor-deploy.tar.gz

echo "✅ 部署完成！"
echo ""
echo "📋 访问信息:"
echo "- HTTP: http://${SERVER_IP}:${PORT}"
echo "- 如果配置了域名: http://${DOMAIN}:${PORT}"
echo ""
echo "🔧 后续步骤:"
echo "1. 配置域名解析指向 ${SERVER_IP} (可选)"
echo "2. 配置防火墙规则开放端口 ${PORT}"
echo "3. 设置监控和日志"
echo "4. 配置SSL反向代理 (如需要HTTPS)"
echo ""
echo "📚 更多信息请查看 deploy/README.md"
