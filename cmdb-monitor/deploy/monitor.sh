#!/bin/bash

# CMDB监控大屏服务监控脚本
# 用于监控服务状态和性能

# 配置
LOG_FILE="/var/log/cmdb-monitor-health.log"
ALERT_EMAIL="<EMAIL>"  # 修改为实际邮箱
DOMAIN="cmdb.yourdomain.com"        # 修改为实际域名
SERVER_IP="************"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a $LOG_FILE
}

# 检查PM2和应用状态
check_pm2() {
    echo -e "${YELLOW}检查PM2和应用状态...${NC}"

    # 检查PM2是否运行
    if ! command -v pm2 &> /dev/null; then
        echo -e "${RED}❌ PM2未安装${NC}"
        log "ERROR: PM2未安装"
        return 1
    fi

    # 检查应用状态
    APP_STATUS=$(pm2 jlist | jq -r '.[0].pm2_env.status' 2>/dev/null || echo "not_found")

    if [ "$APP_STATUS" = "online" ]; then
        echo -e "${GREEN}✅ CMDB应用运行正常${NC}"
        log "INFO: CMDB应用运行正常"
        return 0
    else
        echo -e "${RED}❌ CMDB应用未运行 (状态: $APP_STATUS)${NC}"
        log "ERROR: CMDB应用未运行"

        # 尝试重启应用
        echo "尝试重启应用..."
        cd /opt/cmdb-monitor
        pm2 restart cmdb-monitor 2>/dev/null || pm2 start pm2.config.js
        sleep 5

        APP_STATUS=$(pm2 jlist | jq -r '.[0].pm2_env.status' 2>/dev/null || echo "not_found")
        if [ "$APP_STATUS" = "online" ]; then
            echo -e "${GREEN}✅ 应用重启成功${NC}"
            log "INFO: 应用重启成功"
            return 0
        else
            echo -e "${RED}❌ 应用重启失败${NC}"
            log "ERROR: 应用重启失败"
            return 1
        fi
    fi
}

# 检查网站可访问性
check_website() {
    echo -e "${YELLOW}检查网站可访问性...${NC}"

    # 检查HTTP (端口3000)
    if curl -s -o /dev/null -w "%{http_code}" http://$SERVER_IP:3000 | grep -q "200\|301\|302"; then
        echo -e "${GREEN}✅ HTTP访问正常 (端口3000)${NC}"
        log "INFO: HTTP访问正常"
    else
        echo -e "${RED}❌ HTTP访问异常 (端口3000)${NC}"
        log "ERROR: HTTP访问异常"
        return 1
    fi

    return 0
}

# 检查SSL证书
check_ssl() {
    if [ "$DOMAIN" != "cmdb.yourdomain.com" ]; then
        echo -e "${YELLOW}检查SSL证书...${NC}"
        
        # 检查证书过期时间
        EXPIRY_DATE=$(echo | openssl s_client -servername $DOMAIN -connect $DOMAIN:443 2>/dev/null | openssl x509 -noout -dates | grep notAfter | cut -d= -f2)
        EXPIRY_TIMESTAMP=$(date -d "$EXPIRY_DATE" +%s)
        CURRENT_TIMESTAMP=$(date +%s)
        DAYS_UNTIL_EXPIRY=$(( ($EXPIRY_TIMESTAMP - $CURRENT_TIMESTAMP) / 86400 ))
        
        if [ $DAYS_UNTIL_EXPIRY -gt 30 ]; then
            echo -e "${GREEN}✅ SSL证书有效，还有 $DAYS_UNTIL_EXPIRY 天过期${NC}"
            log "INFO: SSL证书有效，还有 $DAYS_UNTIL_EXPIRY 天过期"
        elif [ $DAYS_UNTIL_EXPIRY -gt 7 ]; then
            echo -e "${YELLOW}⚠️  SSL证书即将过期，还有 $DAYS_UNTIL_EXPIRY 天${NC}"
            log "WARNING: SSL证书即将过期，还有 $DAYS_UNTIL_EXPIRY 天"
        else
            echo -e "${RED}❌ SSL证书即将过期，还有 $DAYS_UNTIL_EXPIRY 天${NC}"
            log "ERROR: SSL证书即将过期，还有 $DAYS_UNTIL_EXPIRY 天"
            return 1
        fi
    fi
    
    return 0
}

# 检查系统资源
check_resources() {
    echo -e "${YELLOW}检查系统资源...${NC}"
    
    # 检查磁盘使用率
    DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ $DISK_USAGE -lt 80 ]; then
        echo -e "${GREEN}✅ 磁盘使用率: ${DISK_USAGE}%${NC}"
        log "INFO: 磁盘使用率正常: ${DISK_USAGE}%"
    elif [ $DISK_USAGE -lt 90 ]; then
        echo -e "${YELLOW}⚠️  磁盘使用率: ${DISK_USAGE}%${NC}"
        log "WARNING: 磁盘使用率较高: ${DISK_USAGE}%"
    else
        echo -e "${RED}❌ 磁盘使用率过高: ${DISK_USAGE}%${NC}"
        log "ERROR: 磁盘使用率过高: ${DISK_USAGE}%"
        return 1
    fi
    
    # 检查内存使用率
    MEMORY_USAGE=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [ $MEMORY_USAGE -lt 80 ]; then
        echo -e "${GREEN}✅ 内存使用率: ${MEMORY_USAGE}%${NC}"
        log "INFO: 内存使用率正常: ${MEMORY_USAGE}%"
    elif [ $MEMORY_USAGE -lt 90 ]; then
        echo -e "${YELLOW}⚠️  内存使用率: ${MEMORY_USAGE}%${NC}"
        log "WARNING: 内存使用率较高: ${MEMORY_USAGE}%"
    else
        echo -e "${RED}❌ 内存使用率过高: ${MEMORY_USAGE}%${NC}"
        log "ERROR: 内存使用率过高: ${MEMORY_USAGE}%"
        return 1
    fi
    
    # 检查CPU负载
    CPU_LOAD=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    CPU_CORES=$(nproc)
    CPU_USAGE=$(echo "scale=0; $CPU_LOAD * 100 / $CPU_CORES" | bc)
    
    if [ $CPU_USAGE -lt 70 ]; then
        echo -e "${GREEN}✅ CPU负载: ${CPU_LOAD} (${CPU_USAGE}%)${NC}"
        log "INFO: CPU负载正常: ${CPU_LOAD}"
    elif [ $CPU_USAGE -lt 90 ]; then
        echo -e "${YELLOW}⚠️  CPU负载: ${CPU_LOAD} (${CPU_USAGE}%)${NC}"
        log "WARNING: CPU负载较高: ${CPU_LOAD}"
    else
        echo -e "${RED}❌ CPU负载过高: ${CPU_LOAD} (${CPU_USAGE}%)${NC}"
        log "ERROR: CPU负载过高: ${CPU_LOAD}"
        return 1
    fi
    
    return 0
}

# 检查日志错误
check_logs() {
    echo -e "${YELLOW}检查错误日志...${NC}"

    # 检查PM2错误日志
    ERROR_COUNT=$(tail -n 100 /var/log/cmdb-monitor/error.log 2>/dev/null | grep -c "$(date '+%Y-%m-%d')" || echo "0")

    if [ $ERROR_COUNT -eq 0 ]; then
        echo -e "${GREEN}✅ 今日无应用错误${NC}"
        log "INFO: 今日无应用错误"
    elif [ $ERROR_COUNT -lt 10 ]; then
        echo -e "${YELLOW}⚠️  今日应用错误: ${ERROR_COUNT} 条${NC}"
        log "WARNING: 今日应用错误: ${ERROR_COUNT} 条"
    else
        echo -e "${RED}❌ 今日应用错误过多: ${ERROR_COUNT} 条${NC}"
        log "ERROR: 今日应用错误过多: ${ERROR_COUNT} 条"
        return 1
    fi

    return 0
}

# 生成报告
generate_report() {
    echo -e "\n${YELLOW}=== CMDB监控大屏健康检查报告 ===${NC}"
    echo "检查时间: $(date)"
    echo "服务器IP: $SERVER_IP"
    echo "域名: $DOMAIN"
    echo ""
    
    TOTAL_CHECKS=0
    FAILED_CHECKS=0
    
    # 执行所有检查
    check_pm2 || ((FAILED_CHECKS++))
    ((TOTAL_CHECKS++))

    check_website || ((FAILED_CHECKS++))
    ((TOTAL_CHECKS++))

    check_resources || ((FAILED_CHECKS++))
    ((TOTAL_CHECKS++))

    check_logs || ((FAILED_CHECKS++))
    ((TOTAL_CHECKS++))
    
    echo ""
    echo -e "${YELLOW}=== 检查结果汇总 ===${NC}"
    echo "总检查项: $TOTAL_CHECKS"
    echo "失败项: $FAILED_CHECKS"
    echo "成功率: $(( (TOTAL_CHECKS - FAILED_CHECKS) * 100 / TOTAL_CHECKS ))%"
    
    if [ $FAILED_CHECKS -eq 0 ]; then
        echo -e "${GREEN}✅ 所有检查项均正常${NC}"
        log "INFO: 健康检查完成，所有项目正常"
        return 0
    else
        echo -e "${RED}❌ 发现 $FAILED_CHECKS 个问题${NC}"
        log "ERROR: 健康检查完成，发现 $FAILED_CHECKS 个问题"
        return 1
    fi
}

# 主函数
main() {
    # 创建日志目录
    mkdir -p $(dirname $LOG_FILE)
    
    # 检查是否为root用户
    if [ "$EUID" -ne 0 ]; then
        echo -e "${RED}❌ 请使用root用户执行此脚本${NC}"
        exit 1
    fi
    
    # 安装必要工具
    if ! command -v bc &> /dev/null; then
        apt update && apt install -y bc
    fi
    
    # 生成报告
    generate_report
    
    exit $?
}

# 如果直接执行脚本
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
