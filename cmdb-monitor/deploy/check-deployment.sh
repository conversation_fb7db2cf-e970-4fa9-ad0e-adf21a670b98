#!/bin/bash

# 部署状态检查脚本
# 用于验证部署是否成功

SERVER_IP="************"
DOMAIN="cmdb.yourdomain.com"  # 修改为实际域名

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 CMDB监控大屏部署状态检查${NC}"
echo "=================================="

# 检查本地构建文件
echo -e "\n${YELLOW}1. 检查本地构建文件...${NC}"
if [ -d "dist" ] && [ -f "dist/index.html" ]; then
    echo -e "${GREEN}✅ 本地构建文件存在${NC}"
    echo "   - 构建目录: dist/"
    echo "   - 文件数量: $(find dist -type f | wc -l)"
    echo "   - 总大小: $(du -sh dist | cut -f1)"
else
    echo -e "${RED}❌ 本地构建文件不存在${NC}"
    echo "   请先运行: npm run build:optimize"
    exit 1
fi

# 检查服务器连接
echo -e "\n${YELLOW}2. 检查服务器连接...${NC}"
if ping -c 1 $SERVER_IP > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 服务器网络连通${NC}"
    echo "   - 服务器IP: $SERVER_IP"
else
    echo -e "${RED}❌ 服务器网络不通${NC}"
    echo "   - 服务器IP: $SERVER_IP"
    exit 1
fi

# 检查HTTP访问
echo -e "\n${YELLOW}3. 检查HTTP访问...${NC}"
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://$SERVER_IP:3000 --connect-timeout 10)
if [ "$HTTP_STATUS" = "200" ] || [ "$HTTP_STATUS" = "301" ] || [ "$HTTP_STATUS" = "302" ]; then
    echo -e "${GREEN}✅ HTTP访问正常${NC}"
    echo "   - 状态码: $HTTP_STATUS"
    echo "   - 访问地址: http://$SERVER_IP:3000"
else
    echo -e "${RED}❌ HTTP访问异常${NC}"
    echo "   - 状态码: $HTTP_STATUS"
    echo "   - 访问地址: http://$SERVER_IP:3000"
fi

# 检查HTTPS访问（如果配置了域名）
if [ "$DOMAIN" != "cmdb.yourdomain.com" ]; then
    echo -e "\n${YELLOW}4. 检查HTTPS访问...${NC}"
    HTTPS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://$DOMAIN --connect-timeout 10)
    if [ "$HTTPS_STATUS" = "200" ]; then
        echo -e "${GREEN}✅ HTTPS访问正常${NC}"
        echo "   - 状态码: $HTTPS_STATUS"
        echo "   - 访问地址: https://$DOMAIN"
    else
        echo -e "${RED}❌ HTTPS访问异常${NC}"
        echo "   - 状态码: $HTTPS_STATUS"
        echo "   - 访问地址: https://$DOMAIN"
    fi
fi

# 检查页面内容
echo -e "\n${YELLOW}5. 检查页面内容...${NC}"
PAGE_CONTENT=$(curl -s http://$SERVER_IP:3000 --connect-timeout 10)
if echo "$PAGE_CONTENT" | grep -q "CMDB"; then
    echo -e "${GREEN}✅ 页面内容正确${NC}"
    echo "   - 包含CMDB关键词"
else
    echo -e "${RED}❌ 页面内容异常${NC}"
    echo "   - 未找到CMDB关键词"
fi

# 检查静态资源
echo -e "\n${YELLOW}6. 检查静态资源...${NC}"
# 提取CSS和JS文件链接
CSS_FILES=$(echo "$PAGE_CONTENT" | grep -o 'href="[^"]*\.css"' | sed 's/href="//g' | sed 's/"//g')
JS_FILES=$(echo "$PAGE_CONTENT" | grep -o 'src="[^"]*\.js"' | sed 's/src="//g' | sed 's/"//g')

CSS_COUNT=0
JS_COUNT=0
CSS_OK=0
JS_OK=0

for css in $CSS_FILES; do
    ((CSS_COUNT++))
    if [[ $css == /* ]]; then
        CSS_URL="http://$SERVER_IP:3000$css"
    else
        CSS_URL="http://$SERVER_IP:3000/$css"
    fi
    
    STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$CSS_URL" --connect-timeout 5)
    if [ "$STATUS" = "200" ]; then
        ((CSS_OK++))
    fi
done

for js in $JS_FILES; do
    ((JS_COUNT++))
    if [[ $js == /* ]]; then
        JS_URL="http://$SERVER_IP:3000$js"
    else
        JS_URL="http://$SERVER_IP:3000/$js"
    fi
    
    STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$JS_URL" --connect-timeout 5)
    if [ "$STATUS" = "200" ]; then
        ((JS_OK++))
    fi
done

if [ $CSS_COUNT -eq $CSS_OK ] && [ $JS_COUNT -eq $JS_OK ]; then
    echo -e "${GREEN}✅ 静态资源加载正常${NC}"
    echo "   - CSS文件: $CSS_OK/$CSS_COUNT"
    echo "   - JS文件: $JS_OK/$JS_COUNT"
else
    echo -e "${RED}❌ 静态资源加载异常${NC}"
    echo "   - CSS文件: $CSS_OK/$CSS_COUNT"
    echo "   - JS文件: $JS_OK/$JS_COUNT"
fi

# 检查响应时间
echo -e "\n${YELLOW}7. 检查响应时间...${NC}"
RESPONSE_TIME=$(curl -s -o /dev/null -w "%{time_total}" http://$SERVER_IP:3000 --connect-timeout 10)
RESPONSE_MS=$(echo "$RESPONSE_TIME * 1000" | bc | cut -d. -f1)

if [ $RESPONSE_MS -lt 1000 ]; then
    echo -e "${GREEN}✅ 响应时间良好${NC}"
    echo "   - 响应时间: ${RESPONSE_MS}ms"
elif [ $RESPONSE_MS -lt 3000 ]; then
    echo -e "${YELLOW}⚠️  响应时间一般${NC}"
    echo "   - 响应时间: ${RESPONSE_MS}ms"
else
    echo -e "${RED}❌ 响应时间过慢${NC}"
    echo "   - 响应时间: ${RESPONSE_MS}ms"
fi

# 生成部署报告
echo -e "\n${BLUE}📋 部署状态报告${NC}"
echo "=================================="
echo "检查时间: $(date)"
echo "服务器IP: $SERVER_IP"
echo "域名: $DOMAIN"
echo ""

# 计算总体状态
TOTAL_CHECKS=6
PASSED_CHECKS=0

# 重新检查各项状态
[ -d "dist" ] && ((PASSED_CHECKS++))
ping -c 1 $SERVER_IP > /dev/null 2>&1 && ((PASSED_CHECKS++))
[ "$HTTP_STATUS" = "200" ] || [ "$HTTP_STATUS" = "301" ] || [ "$HTTP_STATUS" = "302" ] && ((PASSED_CHECKS++))
echo "$PAGE_CONTENT" | grep -q "CMDB" && ((PASSED_CHECKS++))
[ $CSS_COUNT -eq $CSS_OK ] && [ $JS_COUNT -eq $JS_OK ] && ((PASSED_CHECKS++))
[ $RESPONSE_MS -lt 3000 ] && ((PASSED_CHECKS++))

SUCCESS_RATE=$(( PASSED_CHECKS * 100 / TOTAL_CHECKS ))

echo "检查项目: $TOTAL_CHECKS"
echo "通过项目: $PASSED_CHECKS"
echo "成功率: $SUCCESS_RATE%"

if [ $SUCCESS_RATE -ge 80 ]; then
    echo -e "\n${GREEN}🎉 部署状态: 良好${NC}"
    echo "网站已成功部署并可正常访问！"
elif [ $SUCCESS_RATE -ge 60 ]; then
    echo -e "\n${YELLOW}⚠️  部署状态: 一般${NC}"
    echo "网站基本可用，但存在一些问题需要处理。"
else
    echo -e "\n${RED}❌ 部署状态: 异常${NC}"
    echo "部署存在严重问题，需要立即处理。"
fi

echo ""
echo -e "${BLUE}🔗 访问链接:${NC}"
echo "- HTTP: http://$SERVER_IP:3000"
if [ "$DOMAIN" != "cmdb.yourdomain.com" ]; then
    echo "- 域名: http://$DOMAIN:3000"
fi

echo ""
echo -e "${BLUE}📚 相关文档:${NC}"
echo "- 部署指南: DEPLOYMENT.md"
echo "- 详细文档: deploy/README.md"
echo "- 监控脚本: deploy/monitor.sh"
