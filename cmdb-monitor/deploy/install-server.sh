#!/bin/bash

# 服务器端安装脚本（无Nginx版本）
# 在阿里云服务器上执行

set -e

DEPLOY_PATH="/opt/cmdb-monitor"
PORT="3000"

echo "🔧 开始在服务器上安装CMDB监控大屏..."

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用root用户执行此脚本"
    exit 1
fi

# 更新系统包
echo "📦 更新系统包..."
apt update

# 安装必要软件
echo "🛠️ 安装必要软件..."
apt install -y curl wget ufw

# 安装Node.js (使用NodeSource仓库)
echo "📦 安装Node.js..."
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt install -y nodejs

# 验证安装
echo "Node.js版本: $(node --version)"
echo "npm版本: $(npm --version)"

# 安装全局包
echo "📦 安装全局包..."
npm install -g serve pm2

# 创建部署目录
echo "📁 创建部署目录..."
mkdir -p ${DEPLOY_PATH}
mkdir -p /var/log/cmdb-monitor

# 解压应用文件
echo "📦 解压应用文件..."
cd ${DEPLOY_PATH}
tar -xzf /tmp/cmdb-monitor-deploy.tar.gz

# 设置文件权限
echo "🔐 设置文件权限..."
chown -R root:root ${DEPLOY_PATH}
chmod -R 755 ${DEPLOY_PATH}

# 配置PM2
echo "🌐 配置PM2..."
cp /tmp/pm2.config.js ${DEPLOY_PATH}/

# 启动应用
echo "🚀 启动应用..."
cd ${DEPLOY_PATH}
pm2 start pm2.config.js

# 保存PM2配置
pm2 save

# 设置PM2开机自启
pm2 startup
pm2 save

# 配置防火墙
echo "🔥 配置防火墙..."
ufw --force enable
ufw allow ssh
ufw allow ${PORT}/tcp

# 显示状态
echo "📊 服务状态:"
pm2 status

echo "✅ 服务器安装完成！"
echo ""
echo "📋 服务信息:"
echo "- 应用状态: $(pm2 jlist | jq -r '.[0].pm2_env.status' 2>/dev/null || echo 'running')"
echo "- 应用路径: ${DEPLOY_PATH}"
echo "- 访问端口: ${PORT}"
echo "- 日志路径: /var/log/cmdb-monitor/"
echo ""
echo "🔧 下一步:"
echo "1. 访问 http://$(curl -s ifconfig.me):${PORT} 测试应用"
echo "2. 配置域名解析 (可选)"
echo "3. 设置监控和备份"
echo ""
echo "📋 PM2 常用命令:"
echo "- 查看状态: pm2 status"
echo "- 查看日志: pm2 logs cmdb-monitor"
echo "- 重启应用: pm2 restart cmdb-monitor"
echo "- 停止应用: pm2 stop cmdb-monitor"

# 清理临时文件
rm -f /tmp/cmdb-monitor-deploy.tar.gz
rm -f /tmp/pm2.config.js
rm -f /tmp/install-server.sh
