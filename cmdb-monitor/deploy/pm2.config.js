module.exports = {
  apps: [{
    name: 'cmdb-monitor',
    script: 'serve',
    args: [
      '-s', '.',           // 静态文件目录
      '-l', '3000',        // 监听端口
      '--cors',            // 启用CORS
      '--no-clipboard',    // 不复制到剪贴板
      '--no-port-switching' // 不自动切换端口
    ],
    cwd: '/opt/cmdb-monitor',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: '/var/log/cmdb-monitor/error.log',
    out_file: '/var/log/cmdb-monitor/access.log',
    log_file: '/var/log/cmdb-monitor/combined.log',
    time: true,
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    
    // 进程管理配置
    min_uptime: '10s',
    max_restarts: 10,
    restart_delay: 4000,
    
    // 监控配置
    pmx: true,
    
    // 集群配置（如果需要）
    // instances: 'max',
    // exec_mode: 'cluster'
  }]
};
