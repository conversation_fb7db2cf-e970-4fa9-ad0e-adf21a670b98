# CMDB监控大屏 Nginx配置
# 适用于阿里云服务器部署

server {
    listen 80;
    listen [::]:80;
    server_name 59.110.81.13 cmdb.yourdomain.com;  # 根据实际域名修改
    
    # 静态文件根目录
    root /var/www/cmdb-monitor;
    index index.html;
    
    # 访问日志
    access_log /var/log/nginx/cmdb-monitor/access.log;
    error_log /var/log/nginx/cmdb-monitor/error.log;
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self';" always;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json
        image/svg+xml;
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
        
        # 预压缩文件支持
        gzip_static on;
    }
    
    # HTML文件缓存策略
    location ~* \.html$ {
        expires 1h;
        add_header Cache-Control "public, must-revalidate";
    }
    
    # SPA路由支持
    location / {
        try_files $uri $uri/ /index.html;
        
        # 防止缓存index.html
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
    
    # API代理（如果需要后端API）
    location /api/ {
        # proxy_pass http://localhost:3001;  # 根据实际后端地址修改
        # proxy_set_header Host $host;
        # proxy_set_header X-Real-IP $remote_addr;
        # proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        # proxy_set_header X-Forwarded-Proto $scheme;
        
        # 如果没有后端API，返回404
        return 404;
    }
    
    # WebSocket支持（如果需要实时数据）
    location /ws/ {
        # proxy_pass http://localhost:3001;
        # proxy_http_version 1.1;
        # proxy_set_header Upgrade $http_upgrade;
        # proxy_set_header Connection "upgrade";
        # proxy_set_header Host $host;
        # proxy_set_header X-Real-IP $remote_addr;
        # proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        # proxy_set_header X-Forwarded-Proto $scheme;
        
        # 如果没有WebSocket，返回404
        return 404;
    }
    
    # 健康检查端点
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # 隐藏Nginx版本
    server_tokens off;
    
    # 限制请求大小
    client_max_body_size 10M;
    
    # 防止访问隐藏文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 防止访问备份文件
    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }
}

# HTTPS配置（SSL证书配置后启用）
# server {
#     listen 443 ssl http2;
#     listen [::]:443 ssl http2;
#     server_name cmdb.yourdomain.com;
#     
#     # SSL证书配置
#     ssl_certificate /etc/letsencrypt/live/cmdb.yourdomain.com/fullchain.pem;
#     ssl_certificate_key /etc/letsencrypt/live/cmdb.yourdomain.com/privkey.pem;
#     
#     # SSL安全配置
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
#     ssl_prefer_server_ciphers off;
#     ssl_session_cache shared:SSL:10m;
#     ssl_session_timeout 10m;
#     
#     # HSTS
#     add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
#     
#     # 其他配置与HTTP相同...
#     root /var/www/cmdb-monitor;
#     index index.html;
#     
#     # 包含其他location配置...
# }

# HTTP重定向到HTTPS（SSL配置后启用）
# server {
#     listen 80;
#     listen [::]:80;
#     server_name cmdb.yourdomain.com;
#     return 301 https://$server_name$request_uri;
# }
