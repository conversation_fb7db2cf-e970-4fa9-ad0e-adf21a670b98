# CMDB监控大屏部署指南

## 🎯 部署目标
将CMDB监控大屏部署到阿里云服务器 `************`

## 📋 部署前准备

### 1. 服务器要求
- **操作系统**: Ubuntu 20.04+ / CentOS 7+
- **内存**: 最低 2GB，推荐 4GB+
- **存储**: 最低 20GB 可用空间
- **网络**: 公网IP，开放 80/443 端口

### 2. 本地环境要求
- Node.js 16+
- SSH客户端
- 项目已构建完成

## 🚀 快速部署

### 方法一：自动部署脚本

```bash
# 1. 确保项目已构建
npm run build:optimize

# 2. 配置部署参数（编辑 deploy/deploy.sh）
# 修改服务器IP、用户名、域名等信息

# 3. 执行部署
chmod +x deploy/deploy.sh
./deploy/deploy.sh
```

### 方法二：手动部署

#### 步骤1：构建项目
```bash
npm run build:optimize
```

#### 步骤2：上传文件
```bash
# 创建部署包
tar -czf cmdb-monitor.tar.gz -C dist .

# 上传到服务器
scp cmdb-monitor.tar.gz root@************:/tmp/
scp deploy/nginx.conf root@************:/tmp/
scp deploy/install-server.sh root@************:/tmp/
```

#### 步骤3：服务器配置
```bash
# 连接到服务器
ssh root@************

# 执行安装脚本
chmod +x /tmp/install-server.sh
/tmp/install-server.sh
```

## 🔧 服务器配置详解

### 1. 环境安装
```bash
# 更新系统
apt update && apt upgrade -y

# 安装必要软件
apt install -y nginx ufw certbot python3-certbot-nginx curl wget

# 启动服务
systemctl start nginx
systemctl enable nginx
```

### 2. 应用部署
```bash
# 创建目录
mkdir -p /var/www/cmdb-monitor
mkdir -p /var/log/nginx/cmdb-monitor

# 解压应用
cd /var/www/cmdb-monitor
tar -xzf /tmp/cmdb-monitor.tar.gz

# 设置权限
chown -R www-data:www-data /var/www/cmdb-monitor
chmod -R 755 /var/www/cmdb-monitor
```

### 3. Nginx配置
```bash
# 复制配置文件
cp /tmp/nginx.conf /etc/nginx/sites-available/cmdb-monitor

# 启用站点
ln -s /etc/nginx/sites-available/cmdb-monitor /etc/nginx/sites-enabled/

# 删除默认站点
rm -f /etc/nginx/sites-enabled/default

# 测试配置
nginx -t

# 重启Nginx
systemctl restart nginx
```

### 4. 防火墙配置
```bash
# 启用防火墙
ufw --force enable

# 允许必要端口
ufw allow ssh
ufw allow 'Nginx Full'
ufw allow 80
ufw allow 443

# 查看状态
ufw status
```

## 🔐 SSL证书配置

### 使用Let's Encrypt免费证书

```bash
# 申请证书（需要先配置域名解析）
certbot --nginx -d cmdb.yourdomain.com

# 自动续期
crontab -e
# 添加以下行：
# 0 12 * * * /usr/bin/certbot renew --quiet
```

### 手动证书配置

```bash
# 如果有自己的证书文件
mkdir -p /etc/ssl/certs/cmdb-monitor
cp your-cert.pem /etc/ssl/certs/cmdb-monitor/
cp your-key.pem /etc/ssl/private/cmdb-monitor/

# 修改Nginx配置中的证书路径
```

## 📊 监控和维护

### 1. 日志查看
```bash
# Nginx访问日志
tail -f /var/log/nginx/cmdb-monitor/access.log

# Nginx错误日志
tail -f /var/log/nginx/cmdb-monitor/error.log

# 系统日志
journalctl -u nginx -f
```

### 2. 性能监控
```bash
# 查看系统资源
htop
df -h
free -h

# 查看网络连接
netstat -tulpn | grep :80
netstat -tulpn | grep :443
```

### 3. 备份策略
```bash
# 创建备份脚本
cat > /root/backup-cmdb.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf /root/backups/cmdb-monitor-$DATE.tar.gz /var/www/cmdb-monitor
find /root/backups -name "cmdb-monitor-*.tar.gz" -mtime +7 -delete
EOF

chmod +x /root/backup-cmdb.sh

# 添加到定时任务
crontab -e
# 添加：0 2 * * * /root/backup-cmdb.sh
```

## 🔍 故障排除

### 常见问题

#### 1. 无法访问网站
```bash
# 检查Nginx状态
systemctl status nginx

# 检查端口监听
netstat -tulpn | grep :80

# 检查防火墙
ufw status
```

#### 2. 静态资源加载失败
```bash
# 检查文件权限
ls -la /var/www/cmdb-monitor/

# 检查Nginx错误日志
tail -f /var/log/nginx/cmdb-monitor/error.log
```

#### 3. SSL证书问题
```bash
# 检查证书状态
certbot certificates

# 测试SSL配置
openssl s_client -connect yourdomain.com:443
```

## 📈 性能优化

### 1. 启用HTTP/2
```nginx
# 在Nginx配置中添加
listen 443 ssl http2;
```

### 2. 配置缓存
```nginx
# 添加缓存配置
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### 3. 启用Gzip压缩
```nginx
# 已在配置文件中包含
gzip on;
gzip_types text/plain text/css application/javascript;
```

## 🔄 更新部署

### 更新应用
```bash
# 1. 本地构建新版本
npm run build:optimize

# 2. 上传新文件
scp -r dist/* root@************:/var/www/cmdb-monitor/

# 3. 重启Nginx（如果需要）
ssh root@************ "systemctl reload nginx"
```

## 📞 技术支持

如遇到问题，请检查：
1. 服务器日志文件
2. 防火墙配置
3. DNS解析设置
4. SSL证书状态

---

**部署完成后访问**: http://************
