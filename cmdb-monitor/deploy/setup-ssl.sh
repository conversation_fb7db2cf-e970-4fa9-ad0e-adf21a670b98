#!/bin/bash

# SSL证书配置脚本
# 在服务器上执行，用于配置HTTPS

set -e

DOMAIN="cmdb.yourdomain.com"  # 修改为实际域名
EMAIL="<EMAIL>"  # 修改为实际邮箱

echo "🔐 开始配置SSL证书..."

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用root用户执行此脚本"
    exit 1
fi

# 检查域名是否已解析
echo "🔍 检查域名解析..."
if ! nslookup $DOMAIN > /dev/null 2>&1; then
    echo "⚠️  警告: 域名 $DOMAIN 可能未正确解析"
    echo "请确保域名已解析到当前服务器IP"
    read -p "是否继续? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 安装certbot（如果未安装）
if ! command -v certbot &> /dev/null; then
    echo "📦 安装certbot..."
    apt update
    apt install -y certbot python3-certbot-nginx
fi

# 申请SSL证书
echo "📜 申请SSL证书..."
certbot --nginx \
    --non-interactive \
    --agree-tos \
    --email $EMAIL \
    --domains $DOMAIN \
    --redirect

# 测试证书
echo "🧪 测试SSL证书..."
certbot certificates

# 配置自动续期
echo "🔄 配置自动续期..."
(crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet") | crontab -

# 测试自动续期
echo "🧪 测试自动续期..."
certbot renew --dry-run

# 更新Nginx配置以启用HTTPS重定向
echo "🌐 更新Nginx配置..."
cat > /etc/nginx/sites-available/cmdb-monitor << 'EOF'
# HTTP重定向到HTTPS
server {
    listen 80;
    listen [::]:80;
    server_name cmdb.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

# HTTPS配置
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name cmdb.yourdomain.com;
    
    # SSL证书配置（由certbot自动管理）
    ssl_certificate /etc/letsencrypt/live/cmdb.yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/cmdb.yourdomain.com/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;
    
    # 安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # 静态文件根目录
    root /var/www/cmdb-monitor;
    index index.html;
    
    # 访问日志
    access_log /var/log/nginx/cmdb-monitor/access.log;
    error_log /var/log/nginx/cmdb-monitor/error.log;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json
        image/svg+xml;
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
    }
    
    # SPA路由支持
    location / {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
    
    # 健康检查端点
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # 隐藏Nginx版本
    server_tokens off;
    
    # 限制请求大小
    client_max_body_size 10M;
    
    # 防止访问隐藏文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
}
EOF

# 替换域名占位符
sed -i "s/cmdb.yourdomain.com/$DOMAIN/g" /etc/nginx/sites-available/cmdb-monitor

# 测试Nginx配置
echo "🧪 测试Nginx配置..."
nginx -t

# 重启Nginx
echo "🔄 重启Nginx..."
systemctl restart nginx

# 显示SSL证书信息
echo "✅ SSL证书配置完成！"
echo ""
echo "📋 证书信息:"
certbot certificates
echo ""
echo "🌐 访问地址:"
echo "- HTTPS: https://$DOMAIN"
echo "- HTTP: http://$DOMAIN (自动重定向到HTTPS)"
echo ""
echo "🔧 证书管理:"
echo "- 查看证书: certbot certificates"
echo "- 续期证书: certbot renew"
echo "- 撤销证书: certbot revoke --cert-path /etc/letsencrypt/live/$DOMAIN/cert.pem"
echo ""
echo "📅 自动续期已配置，每天12:00检查证书状态"
